import { useState } from 'react';

const hardcodedUrl = 'wss://streamingssss-y0mjc6dh.livekit.cloud';
const hardcodedToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJuYW1lIjoiUHl0aG9uIEJvdCIsInZpZGVvIjp7InJvb21Kb2luIjp0cnVlLCJyb29tIjoibml0eWFzaGEtcm9vbSIsImNhblB1Ymxpc2giOnRydWUsImNhblN1YnNjcmliZSI6dHJ1ZSwiY2FuUHVibGlzaERhdGEiOnRydWV9LCJzdWIiOiJ1c2VyLWFzZHMxMjMiLCJpc3MiOiJBUEk3ZkFZQlA3RGR4QWkiLCJuYmYiOjE3NTI0OTc0NjgsImV4cCI6MTc1MjUxOTA2OH0.JlqcprWQ1yHfjoSdNv4cizTLacdCg-TjAa3EGznSGZA';

export function useConnectionDetails(): ConnectionDetails {
  // Always return your own server and token
  return {
    url: hardcodedUrl,
    token: hardcodedToken,
  };
}

type ConnectionDetails = {
  url: string;
  token: string;
};
