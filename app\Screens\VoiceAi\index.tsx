// app/Screens/VoiceAi/index.tsx
import React, { useEffect, useState } from 'react';
import {
  StyleSheet,
  View,
  Text,
  useColorScheme,
  Pressable,
  ScrollView,
} from 'react-native';
import {
  AudioSession,
  AndroidAudioTypePresets,
  BarVisualizer,
  LiveKitRoom,
  useIOSAudioManagement,
  useLocalParticipant,
  useParticipantTracks,
  useRoomContext,
  useTrackTranscription,
  useVoiceAssistant,
} from '@livekit/react-native';
import { Track } from 'livekit-client';
import { useConnectionDetails } from '@/hooks/useConnectionDetails';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { registerGlobals } from '@livekit/react-native';

registerGlobals();

export default function AssistantScreen() {
  useEffect(() => {
    (async () => {
      // Configure audio session for speakerphone output
      await AudioSession.configureAudio({
        android: {
          preferredOutputList: ['speaker'], // स्पीकर/हेडफोन/ब्लूटूथ पर ऑडियो फोर्स करें
          audioTypeOptions: AndroidAudioTypePresets.communication,
        },
        ios: {
          defaultOutput: 'speaker', // iOS पर स्पीकर को डिफ़ॉल्ट बनाएं
        },
      });
      await AudioSession.startAudioSession();
    })();
    return () => {
      AudioSession.stopAudioSession();
    };
  }, []);

  const connectionDetails = useConnectionDetails();

  return (
    <SafeAreaView style={{ flex: 1 }}>
      <LiveKitRoom
        serverUrl={connectionDetails?.url}
        token={connectionDetails?.token}
        connect={true}
        audio={true}
        video={false}
      >
        <RoomView />
      </LiveKitRoom>
    </SafeAreaView>
  );
}

const RoomView = () => {
  const router = useRouter();
  const room = useRoomContext();
  useIOSAudioManagement(room, true);

  const { isMicrophoneEnabled, localParticipant } = useLocalParticipant();

  const localTracks = useParticipantTracks(
    [Track.Source.Microphone],
    localParticipant.identity
  );
  const micTrack = localTracks[0];

  const {
    segments: userTranscriptions = [],
  } = micTrack ? useTrackTranscription(micTrack) : { segments: [] };

  const { segments: agentTranscriptions = [] } = useVoiceAssistant();

  const [assistantReply, setAssistantReply] = useState<string>('');

  useEffect(() => {
    const handleData = (payload: Uint8Array) => {
      try {
        const decoded = new TextDecoder().decode(payload);
        const { reply } = JSON.parse(decoded);
        if (reply) setAssistantReply(reply);
      } catch (e) {
        console.warn('Failed parsing dataReceived', e);
      }
    };
    room.on('dataReceived', handleData);
    return () => {
      room.off('dataReceived', handleData);
    };
  }, [room]);

  return (
    <View style={styles.container}>
      <BarVisualizer
        state={useVoiceAssistant().state}
        barCount={7}
        options={{ minHeight: 0.5 }}
        trackRef={useVoiceAssistant().audioTrack}
        style={styles.voiceAssistant}
      />

      <ScrollView style={styles.logContainer}>
        {userTranscriptions.map((segment, index) => (
          <Text
            key={index}
            style={[
              styles.userTranscription,
              useColorScheme() === 'light'
                ? styles.userTranscriptionLight
                : styles.userTranscriptionDark,
              useColorScheme() === 'light'
                ? styles.lightThemeText
                : styles.darkThemeText,
            ]}
          >
            {segment.text}
          </Text>
        ))}

        {agentTranscriptions.map((segment, index) => (
          <Text
            key={index}
            style={[
              styles.agentTranscription,
              useColorScheme() === 'light'
                ? styles.lightThemeText
                : styles.darkThemeText,
            ]}
          >
            {segment.text}
          </Text>
        ))}

        {assistantReply ? (
          <Text
            style={[
              styles.agentTranscription,
              styles.assistantReplyBubble,
              useColorScheme() === 'light'
                ? styles.lightThemeText
                : styles.darkThemeText,
            ]}
          >
            {assistantReply}
          </Text>
        ) : null}
      </ScrollView>

      <View style={styles.controlsContainer}>
        <Pressable
          style={({ pressed }) => [
            { backgroundColor: pressed ? '#aaf' : '#07f' },
            styles.button,
          ]}
          onPress={() =>
            localParticipant.setMicrophoneEnabled(!isMicrophoneEnabled)
          }
        >
          <Text style={{ color: '#fff' }}>
            {isMicrophoneEnabled ? 'Mute' : 'Unmute'}
          </Text>
        </Pressable>

        <Pressable
          style={({ pressed }) => [
            { backgroundColor: pressed ? '#faa' : '#f00' },
            styles.button,
          ]}
          onPress={() => {
            router.back();
          }}
        >
          <Text style={{ color: '#fff' }}>Exit</Text>
        </Pressable>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, width: '100%', alignItems: 'center' },
  voiceAssistant: { width: '100%', height: 100 },
  logContainer: { flex: 1, width: '100%' },
  controlsContainer: { flexDirection: 'row', justifyContent: 'center' },
  button: {
    width: 70,
    height: 70,
    margin: 12,
    borderRadius: 35,
    alignItems: 'center',
    justifyContent: 'center',
  },
  userTranscription: {
    alignSelf: 'flex-end',
    margin: 8,
    padding: 8,
    borderRadius: 6,
    fontSize: 18,
  },
  userTranscriptionLight: { backgroundColor: '#B0B0B0' },
  userTranscriptionDark: { backgroundColor: '#404040' },
  agentTranscription: {
    alignSelf: 'flex-start',
    margin: 8,
    padding: 8,
    fontSize: 20,
  },
  assistantReplyBubble: {
    backgroundColor: '#DDEEFF',
    borderRadius: 6,
  },
  lightThemeText: { color: '#000' },
  darkThemeText: { color: '#fff' },
});
